import { manageClientDataManagementToken } from '@/modules/auth/actions/auth-actions'
import { getContractsByIds } from '@/modules/contracts/api/contracts-api'
import { getUserProfile } from '@/modules/profile/api/profile-api'

import { fetchAuthenticatedApi } from '@/lib/fetch-with-auth-api'

import { initializeFormData, stepsToRender } from '../libs/form-data-initializer'
import { mergeProfileDataWithFormSteps } from '../libs/profile-mapper'
import {
  ClientDataManagement,
  ClientDataManagementBackendResponse,
  ContactManagementData,
  ContractsData,
  DataManagementFormConfig,
  DataManagementStep,
  DataManagementStepNames,
} from '../types/data-management-types'
import { DATA_MANAGEMENT_TAGS } from './data-management-tags'

export const getClientDataManagement = async () => {
  const clientDataManagementToken = await manageClientDataManagementToken({
    action: 'get',
  })
  if (!clientDataManagementToken) {
    return {
      serverError: 'No client data management token found',
    }
  }

  // Fetch user profile data to merge with form data
  const userProfileResponse = await getUserProfile()
  const userProfile = userProfileResponse.data || null
  const formDataResponse = await fetchAuthenticatedApi<ClientDataManagementBackendResponse>(
    `/client_data_managements/${clientDataManagementToken}`,
    {
      newApi: true,
      next: {
        revalidate: 60,
        tags: [DATA_MANAGEMENT_TAGS.ALL],
      },
    }
  )

  if (formDataResponse.error) {
    return {
      serverError: 'Failed to fetch client data management form',
    }
  }

  // Extract backend response data - the fetchAuthenticatedApi adds session to the response
  const responseData = formDataResponse?.data

  if (!responseData) {
    return {
      serverError: 'No data received from backend',
    }
  }

  // Extract the main response data (without the added session)
  const { session, ...backendResponse } = responseData

  // Type assertion with proper checking
  const typedBackendResponse = backendResponse

  if (!typedBackendResponse.formConfiguration) {
    return {
      serverError: 'Invalid backend response: missing form configuration',
    }
  }

  // Extract form configuration from backend response
  const formConfigResponse: DataManagementFormConfig = typedBackendResponse.formConfiguration

  // Extract form data from backend response (if exists)
  let formData: ClientDataManagement | null = null

  if (typedBackendResponse.formData) {
    // Cast to any to handle dynamic structure from backend
    const backendFormData: any = typedBackendResponse.formData as any

    // Use the steps data directly from backend (no flattening needed)
    const stepsData = backendFormData.steps || {}

    // Use navigation state directly from backend
    const navigationStateData = backendFormData.navigationState

    // Create navigation state based on existing data or use the one from backend
    let navigationSteps: DataManagementStep[]
    let currentStep: DataManagementStepNames

    if (navigationStateData && navigationStateData.navigationSteps) {
      // Use existing navigation state from backend
      navigationSteps = navigationStateData.navigationSteps
      currentStep = navigationStateData.currentStep
    } else {
      // Create new navigation state
      navigationSteps = stepsToRender(formConfigResponse).map((step, index) => ({
        title: step,
        isActive: index === 0,
        isLast: index === stepsToRender(formConfigResponse).length - 1,
        isCompleted: false, // TODO: Determine completion status based on form data
      }))
      currentStep = navigationSteps[0].title
    }

    // Merge profile data with form steps if profile data is available
    let mergedStepsData = stepsData
    if (userProfile) {
      mergedStepsData = mergeProfileDataWithFormSteps(stepsData, userProfile as unknown as Record<string, unknown>)
    }

    formData = {
      navigationState: {
        navigationSteps: navigationSteps,
        currentStep: currentStep,
      },
      steps: mergedStepsData,
    }
  }

  // If no existing form data, initialize with default structure
  if (!formData) {
    let contractsData: ContractsData | null = null

    const navigationSteps: DataManagementStep[] = stepsToRender(formConfigResponse).map((step, index) => ({
      title: step,
      isActive: index === 0,
      isLast: index === stepsToRender(formConfigResponse).length - 1,
      isCompleted: false,
    }))

    // Load contracts if specified in configuration
    if (formConfigResponse?.contracts?.contractsIds && formConfigResponse?.contracts?.contractsIds?.length > 0) {
      const contractsResult = await getContractsByIds(formConfigResponse?.contracts?.contractsIds)
      const contracts = contractsResult?.data?.map((contract) => ({
        ...contract,
        isUpdated: false,
      })) as ContactManagementData[]
      contractsData = {
        contractsData: contracts,
        canCreateNew: formConfigResponse.contracts.createContracts,
      }
    }

    const stepsInitialData = initializeFormData(stepsToRender(formConfigResponse))
    if (contractsData) {
      stepsInitialData.contracts = contractsData
    }

    // Merge profile data with initial form steps if profile data is available
    let mergedInitialStepsData = stepsInitialData
    if (userProfile) {
      mergedInitialStepsData = mergeProfileDataWithFormSteps(
        stepsInitialData,
        userProfile as unknown as Record<string, unknown>
      )
    }

    formData = {
      navigationState: {
        navigationSteps: navigationSteps,
        currentStep: navigationSteps[0].title,
      },
      steps: mergedInitialStepsData,
    }
  }

  return {
    formData,
    userProfile,
  }
}

// Keep this function for any components that might still need to access form config directly
