import { UserProfileResponse } from '@/modules/auth/types/auth-types'

import { getImageURL, IMAGE_SIZES } from '@/lib/actions/documents/get-image'
import { createDetailedError } from '@/lib/fetch-api'
import { fetchAuthenticatedApi } from '@/lib/fetch-with-auth-api'

import { PROFILE_TAGS } from './profile-tags'

export const getUserProfile = async () => {
  const response = await fetchAuthenticatedApi<UserProfileResponse>('/profile', {
    newApi: true,
    next: {
      revalidate: 180,
      tags: [PROFILE_TAGS.PROFILE],
    },
  })

  console.log('response', response)

  if (response.error) {
    const error = createDetailedError(response.error, response.details)
    return {
      serverError: error,
    }
  }

  const profile = response.data?._embedded?.profile?.[0]
  const accessToken = response.data?.session.accessToken

  if (profile?.imgId && accessToken) {
    profile.imgId = getImageURL({
      documentId: profile.imgId,
      accessToken: accessToken,
      dimensions: IMAGE_SIZES.PROFILE,
    })
  }
  if (profile?.agencyImgId && accessToken) {
    profile.agencyImgId = getImageURL({
      documentId: profile.agencyImgId,
      accessToken: accessToken,
      dimensions: IMAGE_SIZES.THUMBNAIL,
    })
  }

  return { data: profile }
}
